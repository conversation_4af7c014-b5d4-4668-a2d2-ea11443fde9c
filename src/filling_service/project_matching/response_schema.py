from pydantic import Field, BaseModel


class ClientName(BaseModel):
    client_name: str = Field(default="N/A", alias="Client Name")


class DefendantPlaintiff(BaseModel):
    defendant: str = Field(default="N/A", alias="Defendant")
    plaintiff: str = Field(default="N/A", alias="Plaintiff")


# Export the model classes directly, not their schemas
client_name = ClientName
defendant_plaintiff = DefendantPlaintiff