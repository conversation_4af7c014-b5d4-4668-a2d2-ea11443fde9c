from typing import Tu<PERSON>, Dict

from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

class ProjectMatcher:
    def __init__(self, doc_processor):
        self.doc_processor = doc_processor

    def match_project_email(self, email_address, company_data_destination_id):

        project_id = self.doc_processor.find_matching_project_by_email(email_address,
                                                                  company_data_destination_id=6)
        return project_id

    def match_on_project_id(self, project_id, company_data_destination_id):

        project_found = self.doc_processor.find_matching_project_by_project_id(project_id,
                                                                            company_data_destination_id=6)
        return project_found

    def match_on_client_name(self, client_name, company_data_destination_id):

        project_found = self.doc_processor.find_matching_project_by_client_name(client_name,
                                                                            company_data_destination_id=6)
        return project_found

    def match_project_by_secondary_identifiers(self, metadata, company_data_destination_id):

        project_id, matches, mismatches = self.doc_processor.find_project_by_field_priority(metadata,
                                                                                            company_data_destination_id=6)
        return project_id, matches, mismatches

    def evaluate_match_confidence(self, project_id: str, matches: Dict[str, Tuple[str, str]]) -> <PERSON>ple[str, bool]:
        """
        Evaluates the match confidence based on which fields matched.

        Returns:
            Tuple containing:
            - project_id
            - manual_review flag (True if human review is recommended)
        """

        matched_keys = set(matches.keys())

        has_client_name = "project_or_client_name" in matched_keys
        has_email = "project_email_address" in matched_keys

        # We treat project_id as matched since it's already used to fetch the project
        has_project_id = True

        # Case 1: Strong combinations (no manual review needed)
        if has_project_id and has_client_name:
            return project_id, False
        if has_project_id and has_email:
            return project_id, False
        if has_email and has_client_name:
            return project_id, False

        # Case 2: Strong metadata support
        metadata_fields = {"lead_attorney", "incident_type", "incident_date"}
        if (has_project_id or has_email or has_client_name) and metadata_fields & matched_keys:
            return project_id, False

        # All other cases → manual review recommended
        return project_id, True

    def verify_secondary_metadata(
            self,
            project_id,
            metadata,
            company_data_destination_id,
            check_email: bool = True,
            check_client_name: bool = True
    ) -> Dict[str, Tuple[str, str]]:
        """
        Verifies additional metadata fields (client name, email, and others) for a matched project.
        Returns only matched fields.
        """

        matches: Dict[str, Tuple[str, str]] = {}

        # 1. Check client name
        if check_client_name:
            client_name = getattr(metadata, "project_or_client_name", None)
            if client_name:
                matched_id = self.doc_processor.find_matching_project_by_client_name(
                    client_name, company_data_destination_id
                )
                if str(matched_id) == str(project_id):
                    matches["project_or_client_name"] = (client_name, client_name)

        # 2. Check project email address
        if check_email:
            email = getattr(metadata, "project_email_address", None)
            if email:
                matched_id = self.doc_processor.find_matching_project_by_email(
                    email, company_data_destination_id
                )
                if str(matched_id) == str(project_id):
                    matches["project_email_address"] = (email, email)

        # 3. Compare other metadata fields
        extra_matches, _ = self.doc_processor.compare_metadata_fields_with_project(
            filevine_project_id=project_id,
            metadata=metadata
        )

        # Merge all matched fields
        matches.update(extra_matches)

        return matches

    def match_project(self, metadata, company_data_destination_id):
        # 1. Try Project ID
        if metadata.project_id:
            project_id = self.doc_processor.find_matching_project_by_project_id(
                metadata.project_id, company_data_destination_id
            )
            if project_id:
                logger.info(f"Matched by Project ID: {project_id}")
                matches = self.verify_secondary_metadata(
                    project_id, metadata, company_data_destination_id
                )
                return self.evaluate_match_confidence(project_id, matches)

        # 2. Try Project Email
        email = getattr(metadata, "project_email_address", None)
        if email:
            email_project_id = self.doc_processor.find_matching_project_by_email(
                email, company_data_destination_id
            )
            if email_project_id:
                logger.info(f"Matched by Email: {email_project_id}")
                matches = self.verify_secondary_metadata(
                    email_project_id, metadata, company_data_destination_id, check_email=False
                )
                return self.evaluate_match_confidence(email_project_id, matches)

        # 3. Try Client Name
        client_name = getattr(metadata, "project_or_client_name", None)
        if client_name:
            name_project_id = self.doc_processor.find_matching_project_by_client_name(
                client_name, company_data_destination_id
            )
            if name_project_id:
                logger.info(f"Matched by Client Name: {name_project_id}")
                matches = self.verify_secondary_metadata(
                    name_project_id, metadata, company_data_destination_id, check_email=False, check_client_name=False
                )
                return self.evaluate_match_confidence(name_project_id, matches)

        # 4. Fallback to secondary metadata
        project_ids, matches, mismatches = self.doc_processor.find_project_by_field_priority(
            metadata, company_data_destination_id,
        )
        if project_ids:
            logger.info(f"Matched by secondary metadata: {project_ids}")
            return (project_ids[0] if len(project_ids) == 1 else project_ids), True

        logger.warning("No matching project found.")
        return None, True

    # def match_project(self, metadata, company_data_destination_id):
    #     # 1. Try Project ID
    #     if metadata.project_id:
    #         project_id = self.doc_processor.find_matching_project_by_project_id(
    #             metadata.project_id, company_data_destination_id
    #         )
    #         if project_id:
    #             logger.info(f"Matched by Project ID: {project_id}")
    #             matches = self.verify_secondary_metadata(
    #                 project_id, metadata, company_data_destination_id
    #             )
    #             return project_id, matches
    #
    #     # 2. Try Project Email
    #     email = getattr(metadata, "project_email_address", None)
    #     if email:
    #         email_project_id = self.doc_processor.find_matching_project_by_email(
    #             email, company_data_destination_id
    #         )
    #         if email_project_id:
    #             logger.info(f" Matched by Email: {email_project_id}")
    #             matches = self.verify_secondary_metadata(
    #                 email_project_id, metadata, company_data_destination_id, check_email=False
    #             )
    #             return email_project_id, matches
    #
    #     # 3. Try Client Name
    #     client_name = getattr(metadata, "project_or_client_name", None)
    #     if client_name:
    #         name_project_id = self.doc_processor.find_matching_project_by_client_name(
    #             client_name, company_data_destination_id
    #         )
    #         if name_project_id:
    #             logger.info(f" Matched by Client Name: {name_project_id}")
    #             matches = self.verify_secondary_metadata(
    #                 name_project_id, metadata, company_data_destination_id, check_email=False, check_client_name=False
    #             )
    #             return name_project_id, matches
    #
    #     # 4. Try fallback secondary metadata fields
    #     project_ids, matches, mismatches = self.doc_processor.find_project_by_field_priority(
    #         metadata, company_data_destination_id,
    #     )
    #     if project_ids:
    #         logger.info(f" Matched by secondary metadata: {project_ids}")
    #         return project_ids[0] if len(project_ids) == 1 else project_ids, manual_review =True
    #
    #     # No match
    #     logger.warning(" No matching project found.")
    #     return None
    #
