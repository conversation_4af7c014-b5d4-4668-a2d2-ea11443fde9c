from src.classifier_service.common.utils.service_status_logging import _log_failure
from src.filling_service.config import extractor, project_matcher
from src.filling_service.project_matching_deprecated.fetch_email_info import get_email_info
from src.shared.service_registry import doc_processor
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)


def match_project(doc_id: int, company_data_destination_id: int = 6):
    """
    Matches a document to a Filevine project using extracted metadata.

    Args:
        doc_id (int): ID of the document to match.
        company_data_destination_id (int): Destination ID for Filevine company.

    Behavior:
        - Logs the outcome.
        - Marks document for manual review if matching fails or is ambiguous.
    """
    service_id = 5

    try:
        logger.info(f"[PROJECT MATCH] Starting project match for doc_id={doc_id}")

        # Step 1: Extract metadata from email
        email_info = get_email_info(doc_id)
        if not email_info:
            logger.warning("No email_info returned for document.")
            doc_processor.mark_document_for_manual_review(doc_id)
            _log_failure(doc_id, service_id, "Failed to retrieve email info.")
            return

        logger.debug(f"Extracted email info of type: {type(email_info)}")

        # Step 2: Extract structured fields
        matched_fields = extractor.match_field_values(email_info)
        if not matched_fields or not isinstance(matched_fields, list):
            logger.warning("No metadata fields matched or invalid format.")
            doc_processor.mark_document_for_manual_review(doc_id)
            _log_failure(doc_id, service_id, "Failed to match field values.")
            return

        logger.info(f"Matched fields: {matched_fields}")

        # Step 3: Select the first metadata object for matching
        metadata = matched_fields[0]
        if not hasattr(metadata, "project_id"):
            logger.warning("Extracted metadata is missing required fields.")
            doc_processor.mark_document_for_manual_review(doc_id)
            _log_failure(doc_id, service_id, "Missing required fields in metadata.")
            return

        # Step 4: Attempt to match the project
        try:
            project_id, manual_review = project_matcher.match_project(
                metadata=metadata,
                company_data_destination_id=company_data_destination_id
            )
        except Exception as e:
            logger.error(f"[MATCHING ERROR] {e}", exc_info=True)
            doc_processor.mark_document_for_manual_review(doc_id)
            _log_failure(doc_id, service_id, "Project matching failed.")
            return

        # Step 5: Handle result
        if project_id:
            logger.info(f" Matched Project ID: {project_id}")
            if manual_review:
                logger.info(" Match requires manual review.")
                doc_processor.mark_document_for_manual_review(doc_id)
        else:
            logger.warning(" No project matched.")
            doc_processor.mark_document_for_manual_review(doc_id)
            _log_failure(doc_id, service_id, "No project matched.")

    except Exception as e:
        logger.error(f"[FATAL] Error in match_project for doc_id={doc_id}: {e}", exc_info=True)
        doc_processor.mark_document_for_manual_review(doc_id)
        _log_failure(doc_id, service_id)

# match_project(268)