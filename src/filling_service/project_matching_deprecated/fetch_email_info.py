from src.filling_service.project_matching_deprecated.utils import extract_plain_text_from_email_body
from src.shared.service_registry import doc_processor
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)
# def get_email_info(doc_id):
#     """
#     Extract and process email metadata for project matching.
#
#     Args:
#         doc_id (int): The document ID to process
#
#     Returns:
#         dict: Dictionary containing extracted email metadata including subject,
#               plain text content, sender info, and recipients
#     """
#     # Get email metadata
#     email_info = doc_processor.get_document_info(doc_id)
#     if not email_info:
#         raise ValueError("Failed to retrieve document metadata.")
#
#     # email_info is already a dictionary, no need to parse JSON
#     metadata = email_info.get("metadata", {})
#     subject = metadata.get("subject", "")
#
#     # Extract plain text from email body using the email_info dictionary directly
#     plain_text = extract_plain_text_from_email_body(email_info)
#
#     sender = metadata.get("sender", "")
#     sent_from = metadata.get("from", "")
#     to_recipients = metadata.get("to_recipients", "")
#     cc_recipients = metadata.get("cc_recipients", "")
#
#     print(f"Email Metadata --- Subject: {subject},\n\n Email Text: {plain_text},\n\n Sender: {sender}\n'n, Sent From: {sent_from}\n\n, To Recipients: {to_recipients}\n\n, CC Recipients: {cc_recipients}")
#
#     return {
#         'subject': subject,
#         'plain_text': plain_text,
#         'sender': sender,
#         'sent_from': sent_from,
#         'to_recipients': to_recipients,
#         'cc_recipients': cc_recipients,
#         'metadata': metadata
#     }
def get_email_info(doc_id: int) -> str:
    """
    Retrieves and formats email metadata and body for LLM-based project matching.

    Args:
        doc_id (int): The document ID to process.

    Returns:
        str: A formatted string representation of the email metadata and content.
    """
    email_info = doc_processor.get_document_info(doc_id)
    if not email_info:
        raise ValueError("Failed to retrieve document metadata.")

    metadata = email_info.get("metadata", {})
    subject = metadata.get("subject", "")
    plain_text = extract_plain_text_from_email_body(email_info)

    sender = metadata.get("sender", "")
    sent_from = metadata.get("from", "")
    to_recipients = metadata.get("to_recipients", "")
    cc_recipients = metadata.get("cc_recipients", "")

    formatted = (
        f"Subject: {subject}\n"
        f"Sender: {sender}\n"
        f"Sent From: {sent_from}\n"
        f"To Recipients: {to_recipients}\n"
        f"CC Recipients: {cc_recipients}\n\n"
        f"Email Body:\n{plain_text}"
    )

    logger.debug(f"[EMAIL INFO] Formatted email content for doc ID {doc_id}:\n{formatted}")
    return formatted


# if __name__ == "__main__":
#     # Test the function with document ID 268
#     result = get_email_info(268)
#     print("Function completed successfully!")







