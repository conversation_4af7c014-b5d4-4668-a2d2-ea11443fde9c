from src.filling_service.config import project_matcher
from src.filling_service.project_matching_deprecated.response_schema import FilingMetadata
from src.shared.service_registry import doc_processor

# result = doc_processor.find_matching_project_by_email("<EMAIL>", company_data_destination_id=6)
# print(result)


# result = doc_processor.find_matching_project_by_project_id(*********, company_data_destination_id=6)
# print(result)

# result = doc_processor.find_matching_project_by_client_name("Carmin Lovelesd", company_data_destination_id=6)
# print(result)

metadata = FilingMetadata(
    **{
        "Incident Date": "2024-01-15",
        "Incident Type": "St. Bernards",
        "Lead Attorney": "NULL",
        "First Primary": "Evelyn",
        "Project Number": "*********",
        "SOL (date only)": "2025-01-15",
        "Meds Total Balance Due (currency)": "N/A",
        "project email address": "<EMAIL>",
        "project name or client name or both": "Carmin Lovelesd",
        "Phase Name": "N/A",
    }
)



# matched, mismatched = doc_processor.compare_metadata_fields_with_project(1, metadata)
# print(f"Matched: {matched}, \n\n Mismatched: {mismatched}")

# result = doc_processor.find_project_by_field_priority(metadata, company_data_destination_id=6)
#
# if result:
#     project_id, matched, mismatched = result
#     print(f"\n Project ID Match: {project_id}")
#     print(f"  Matched Fields: {matched}")
#     print(f"  Mismatched Fields: {mismatched}")
# else:
#     print(" No project match found.")

# result = project_matcher.match_project(metadata, company_data_destination_id=6)
# print(result)
# result = project_matcher.verify_secondary_metadata(*********, metadata, company_data_destination_id=6)
# print(result)
