from typing import Dict
from src.classifier_service.common.utils.clean_storage_name import extract_blob_name
from src.classifier_service.common.utils.service_status_logging import _log_failure
from src.filling_service.fields_extraction.extractor_run import execute_extraction_pipeline
from src.filling_service.naming_conventions.doc_naming import name_generation
from src.filling_service.project_matching_deprecated.match_project import match_project
from src.shared.service_registry import doc_processor, blob_client
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

# TODO update doc_type_id
def run_filling_pipeline(doc_id: int) -> Dict[str, str]:
    """
    Main pipeline to orchestrate field extraction, name generation, and project matching.

    Args:
        doc_id (int): Unique document ID.

    Returns:
        Dict[str, str]: Extracted field values.
    """
    try:
        logger.info(f"[PIPELINE] Starting full filling pipeline for doc ID {doc_id}")

        # 1. Get metadata
        email_info = doc_processor.get_document_info(doc_id)
        if not email_info:
            _log_failure(doc_id, 2, "Metadata not found")
            return {}

        document_location = email_info.get("storage_location")
        doc_type_id = email_info.get("document_type_id")
        company_data_destination_id = email_info.get("company_data_destination_id")
        if not company_data_destination_id:
            logger.warning(f"Missing company_data_destination_id for doc_id={doc_id}, using default=6")
            company_data_destination_id = 6

        company_id = doc_processor.get_company_id_by_document_id(doc_id)
        blob_name = extract_blob_name(document_location)
        container_name = f"company-{company_id}"

        # 2. Get document preview
        preview_bytes = blob_client.get_document_preview(container_name, blob_name, num_pages=5)
        if not preview_bytes:
            _log_failure(doc_id, 2, "Failed to retrieve preview content")
            return {}

        # 3. Extract fields
        extracted_field_values = execute_extraction_pipeline(
            doc_id=doc_id,
            doc_type_id=doc_type_id,
            preview_bytes=preview_bytes
        )

        # 4. Generate document name
        name_generation(
            doc_id=doc_id,
            extracted_field_values=extracted_field_values,
            preview_bytes=preview_bytes,
            doc_type_id=101,
            company_data_destination_id=company_data_destination_id
        )

        # 5. Project Matching
        match_project(doc_id, company_data_destination_id=company_data_destination_id)

        return extracted_field_values

    except Exception as e:
        logger.error(f"[PIPELINE] Unhandled error in pipeline for doc ID {doc_id}: {e}", exc_info=True)
        return {}

run_filling_pipeline(268)