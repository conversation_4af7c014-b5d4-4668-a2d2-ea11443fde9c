from datetime import datetime
import json
from typing import <PERSON><PERSON>, Dict, <PERSON><PERSON>, List

from sqlalchemy import func
from sqlalchemy.exc import NoResultFound

from src.filling_service.project_matching_deprecated.response_schema import FilingMetadata
from src.shared.data_sources.db_models import Document, DocumentField, DocumentServiceStatus, DocumentFieldValue, \
    DocumentType, \
    NameConvention, CompanyDataSource, FilevineField, FilevineProject
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

class DocumentProcessorService:
    def __init__(self, db_client):
        self.db_client = db_client

    def test_connection(self) -> bool:
        return self.db_client.test_connection()

    def get_document_info(self, document_id: int) -> dict:
        with self.db_client.session_scope() as session:
            try:
                document = session.query(Document).filter_by(id=document_id).one()

                #  Parse the metadata JSON safely
                metadata = {}
                if document.document_metadata:
                    try:
                        metadata = json.loads(document.document_metadata)
                    except json.JSONDecodeError as json_err:
                        raise ValueError(f"Invalid JSON in metadata for document {document_id}: {json_err}")

                return {
                    "original_name": document.original_name,
                    "storage_location": document.storage_location,
                    "metadata": metadata,
                    "document_type_id": document.document_type_id
                }

            except NoResultFound:
                raise ValueError(f"Document with ID {document_id} not found")
            except Exception as e:
                raise RuntimeError(f"Error retrieving document info: {e}")

    def get_company_id_by_document_id(self, document_id: int) -> int:
        """
        Retrieves the company_id associated with the given document_id
        by joining the document and company_data_source tables.
        """
        with self.db_client.session_scope() as session:
            try:
                result = (
                    session.query(CompanyDataSource.company_id)
                    .join(Document, CompanyDataSource.id == Document.company_data_source_id)
                    .filter(Document.id == document_id)
                    .one_or_none()
                )

                if result:
                    return result.company_id
                else:
                    raise ValueError(f"No company found for document_id={document_id}")
            except Exception as e:
                raise RuntimeError(f"Error retrieving company_id for document_id={document_id}: {e}")

    def log_document_type_id(self, document_id: int, document_type_id: int) -> None:
        """
        Updates the document_type_id for the given document ID.
        """
        with self.db_client.session_scope() as session:
            try:
                document = session.query(Document).filter_by(id=document_id).one()
                document.document_type_id = document_type_id
                document.updated_at = datetime.utcnow()
                logger.info(f"Updated document_type_id for Document ID {document_id} to {document_type_id}")
            except NoResultFound:
                logger.warning(f"No document found with ID: {document_id}")
            except Exception as e:
                logger.error(f"Error updating document_type_id for Document ID {document_id}: {e}")

    def get_fields_by_document_type_id(self, document_type_id: int) -> list[str]:
        """
        Returns a list of field names associated with the given document_type_id.
        """
        with self.db_client.session_scope() as session:
            try:
                fields = (
                    session.query(DocumentField.field_name)
                    .filter_by(document_type_id=document_type_id)
                    .order_by(DocumentField.id)
                    .all()
                )
                return [f.field_name for f in fields]

            except Exception as e:
                raise RuntimeError(f"Error fetching fields for document_type_id {document_type_id}: {e}")

    def insert_field_value_for_document(self, document_id: int, field_name: str, field_value: str) -> None:
        """
        Inserts an extracted field value into the document_field_values table for a given document and field name.
        """
        with self.db_client.session_scope() as session:
            try:
                # Step 1: Get the document_type_id for this document
                document = session.query(Document).filter_by(id=document_id).one()
                document_type_id = document.document_type_id

                # Step 2: Look up the field by name and document_type_id
                field = session.query(DocumentField).filter_by(
                    document_type_id=document_type_id,
                    field_name=field_name
                ).one()

                # Step 3: Insert into document_field_values
                new_value = DocumentFieldValue(
                    document_id=document_id,
                    document_field_id=field.id,
                    field_value=field_value
                )
                session.add(new_value)
                logger.debug(f"Inserting value for doc {document_id}, field '{field_name}': {field_value}")

            except NoResultFound:
                raise ValueError(f"Field '{field_name}' not found for document_type_id={document_type_id}")
            except Exception as e:
                raise RuntimeError(f"Error inserting field value: {e}")

    def log_document_service_status(
            self,
            document_id: int,
            service_id: int,
            status_id: int,
            metadata: str=None
    ) -> None:
        """
        Logs a new entry into the document_service_status table with the given document_id,
        service_id, status_id, and optional JSON metadata.
        """
        with self.db_client.session_scope() as session:
            try:
                new_entry = DocumentServiceStatus(
                    document_id=document_id,
                    service_id=service_id,
                    status_id=status_id,
                    service_metadata=metadata,
                )
                session.add(new_entry)
                logger.info(
                    f"Logged document service status: document_id={document_id}, "
                    f"service_id={service_id}, status_id={status_id}, metadata={metadata}"
                )
            except Exception as e:
                logger.error(f"Error logging document service status: {e}")

    def get_document_type_id_by_doc_type_name(self, type_name: str) -> int:
        """
        Given a document type name (e.g., 'Affidavit'), returns the corresponding document_type_id.
        Raises ValueError if not found.
        """
        with self.db_client.session_scope() as session:
            try:
                doc_type = (
                    session.query(DocumentType)
                    .filter(DocumentType.type == type_name)
                    .one()
                )
                return doc_type.id
            except NoResultFound:
                raise ValueError(f"Document type '{type_name}' not found.")
            except Exception as e:
                raise RuntimeError(f"Error retrieving document_type_id for '{type_name}': {e}")

    def log_document_name(self, document_id: int, document_name: str) -> None:
        """
        Updates the document_name for the given document ID.
        """
        with self.db_client.session_scope() as session:
            try:
                document = session.query(Document).filter_by(id=document_id).one()
                document.document_name = document_name
                document.updated_at = datetime.utcnow()
                logger.info(f"Set document type id to: {document_name}")
            except NoResultFound:
                logger.warning(f"No document found with ID: {document_id}")
            except Exception as e:
                logger.error(f"Error updating document_name for Document ID {document_id}: {e}")

    def get_naming_template_by_document_type_and_destination(
            self,
            document_type_id: int,
            company_data_destination_id: int
    ) -> str:
        """
        Retrieves the naming template for a specific document_type_id and company_data_destination_id.
        """
        with self.db_client.session_scope() as session:
            try:
                naming = (
                    session.query(NameConvention)
                    .filter_by(
                        document_type_id=document_type_id,
                        company_data_destination_id=company_data_destination_id
                    )
                    .one_or_none()
                )

                if naming:
                    return naming.name_template
                else:
                    raise ValueError(
                        f"No naming template found for document_type_id={document_type_id} "
                        f"and company_data_destination_id={company_data_destination_id}"
                    )
            except Exception as e:
                raise RuntimeError(
                    f"Error retrieving naming template for document_type_id={document_type_id} "
                    f"and company_data_destination_id={company_data_destination_id}: {e}"
                )
    def _get_all_document_types(self):
        with self.db_client.session_scope() as session:
            try:
                document_types = session.query(DocumentType).all()
                doc_types = [doc_type.type for doc_type in document_types]
                return doc_types
            except Exception as e:
                raise RuntimeError(f"Error retrieving all document types: {e}")

    def find_matching_project_by_email(
            self,
            project_email_address: str,
            company_data_destination_id: int
    ) -> Optional[str]:
        if not project_email_address:
            logger.warning("[MATCHING] No project_email_address provided.")
            return None

        with self.db_client.session_scope() as session:
            try:
                project = (
                    session.query(FilevineProject)
                    .filter(
                        FilevineProject.company_data_destination_id == company_data_destination_id,
                        FilevineProject.project_email_address.ilike(f"%{project_email_address}%")
                    )
                    .first()
                )

                if project:
                    logger.info(
                        f"[MATCHING] Found Filevine project_id: {project.project_id} for email: {project_email_address}"
                    )
                    return project.project_id
                else:
                    logger.warning(f"[MATCHING] No Filevine project found for email: {project_email_address}")
                    return None

            except Exception as e:
                logger.error(f"[MATCHING] Error matching by email: {e}", exc_info=True)
                return None

    def find_matching_project_by_client_name(
            self,
            client_name: str,
            company_data_destination_id: int
    ) -> Optional[str]:
        if not client_name:
            logger.warning("[MATCHING] No client_name provided.")
            return None

        with self.db_client.session_scope() as session:
            try:
                project = (
                    session.query(FilevineProject)
                    .filter(
                        FilevineProject.company_data_destination_id == company_data_destination_id,
                        FilevineProject.client_name.ilike(f"%{client_name}%")
                    )
                    .first()
                )

                if project:
                    logger.info(
                        f"[MATCHING] Found Filevine project_id: {project.project_id} for client name: {client_name}"
                    )
                    return project.project_id
                else:
                    logger.warning(f"[MATCHING] No Filevine project found for client name: {client_name}")
                    return None

            except Exception as e:
                logger.error(f"[MATCHING] Error matching by client name: {e}", exc_info=True)
                return None

    def find_matching_project_by_project_id(
            self,
            project_id_str: str,
            company_data_destination_id: int
    ) -> Optional[str]:
        if not project_id_str:
            logger.warning("[MATCHING] No project_id provided.")
            return None

        with self.db_client.session_scope() as session:
            try:
                project = (
                    session.query(FilevineProject)
                    .filter(
                        FilevineProject.company_data_destination_id == company_data_destination_id,
                        FilevineProject.project_id == project_id_str
                    )
                    .first()
                )

                if project:
                    logger.info(
                        f"[MATCHING] Found Filevine project_id: {project.project_id} for project_id: {project_id_str}"
                    )
                    return project.project_id
                else:
                    logger.warning(f"[MATCHING] No Filevine project found for project_id: {project_id_str}")
                    return None

            except Exception as e:
                logger.error(f"[MATCHING] Error matching by project_id: {e}", exc_info=True)
                return None

    def compare_metadata_fields_with_project(
            self,
            filevine_project_id: int,
            metadata: FilingMetadata
    ) -> Tuple[Dict[str, Tuple[str, str]], Dict[str, Tuple[str, str]]]:
        """
        Compares extracted FilingMetadata values to DB field values using 'friendly_name'.

        Returns:
            Tuple of two dicts:
                - matches:    {field_key: (metadata_value, db_value)}
                - mismatches: {field_key: (metadata_value, db_value)}
        """
        with self.db_client.session_scope() as session:
            try:
                # Get all fields for the project
                fields = session.query(FilevineField).filter_by(
                    filevine_project_id=filevine_project_id
                ).all()

                # Map from lowercase friendly name to field value
                field_map = {
                    f.friendly_name.strip().lower(): f.field_value
                    for f in fields if f.friendly_name
                }

                # Pydantic alias -> attribute name mapping
                alias_map = {
                    field.alias.strip().lower(): attr_name
                    for attr_name, field in metadata.__fields__.items()
                }

                matches = {}
                mismatches = {}

                for friendly, attr in alias_map.items():
                    meta_val = getattr(metadata, attr)
                    db_val = field_map.get(friendly)

                    if not meta_val or not db_val:
                        continue  # Skip comparison if any value is missing

                    if str(meta_val).strip() == str(db_val).strip():
                        matches[attr] = (meta_val, db_val)
                    else:
                        mismatches[attr] = (meta_val, db_val)

                return matches, mismatches

            except Exception as e:
                logger.error(
                    f"[MATCHING] Failed to compare fields for project {filevine_project_id}: {e}",
                    exc_info=True
                )
                return {}, {}

    def find_project_by_field_priority(
            self,
            metadata: FilingMetadata,
            company_data_destination_id: int
    ) -> Tuple[List[int], Dict[int, Dict[str, Tuple[str, str]]], Dict[int, Dict[str, Tuple[str, str]]]]:
        """
        Matches Filevine projects based on prioritized metadata fields.
        Returns all project IDs with the highest number of matched fields.

        Returns:
            - List of matching project IDs.
            - Dict of matched fields per project_id.
            - Dict of mismatched fields per project_id.
        """
        with self.db_client.session_scope() as session:
            try:
                alias_map = {
                    attr: field.alias.strip()
                    for attr, field in metadata.__fields__.items()
                }

                def get_candidates(field_name: str, value: str) -> List[int]:
                    return [
                        pid for (pid,) in session.query(FilevineField.filevine_project_id)
                        .filter(
                            func.lower(func.trim(FilevineField.friendly_name)) == field_name.lower().strip(),
                            func.lower(func.trim(FilevineField.field_value)) == value.lower().strip(),
                            FilevineField.filevine_project_id.in_(
                                session.query(FilevineProject.id).filter(
                                    FilevineProject.company_data_destination_id == company_data_destination_id
                                )
                            )
                        ).distinct().all()
                    ]

                def compare_fields(project_id: int):
                    return self.compare_metadata_fields_with_project(
                        filevine_project_id=project_id,
                        metadata=metadata
                    )

                candidates: List[Tuple[int, Dict[str, Tuple[str, str]], Dict[str, Tuple[str, str]]]] = []

                def evaluate(project_ids, check_condition_fn):
                    for pid in project_ids:
                        matches, mismatches = compare_fields(pid)
                        if check_condition_fn(matches):
                            candidates.append((pid, matches, mismatches))

                # Define extended priority list
                primary_fields = [
                    ("lead_attorney", lambda m: "first_primary" in m),
                    ("first_primary", lambda m: any(k in m for k in
                                                    ["incident_type", "incident_date", "phase_name", "sol_date_only",
                                                     "meds_total_balance_due"])),
                    ("project_email_address", lambda m: "project_or_client_name" in m or any(
                        k in m for k in ["incident_type", "incident_date"])),
                    ("project_or_client_name", lambda m: any(k in m for k in ["incident_type", "incident_date"]))
                ]

                # Step 1: Try all primary fields with their respective rules
                for field_key, condition in primary_fields:
                    val = getattr(metadata, field_key, None)
                    if not val or val.upper() == "N/A":
                        continue
                    friendly_name = alias_map.get(field_key)
                    logger.info(f"[MATCHING] Searching by {field_key.replace('_', ' ').title()}: {val}")
                    ids = get_candidates(friendly_name, val)
                    evaluate(ids, condition)

                # Step 2: Fallback fields
                fallback_fields = ["incident_type", "incident_date", "phase_name", "sol_date_only",
                                   "meds_total_balance_due"]

                for field in fallback_fields:
                    val = getattr(metadata, field)
                    if not val or val.upper() == "N/A":
                        continue

                    fname = alias_map.get(field)
                    logger.info(f"[MATCHING] Fallback search by {field}: {val}")
                    ids = get_candidates(fname, val)

                    evaluate(ids, lambda m: bool(m))

                # Analyze all matches
                if not candidates:
                    logger.warning("[MATCHING] ❌ No matching project found.")
                    return [], {}, {}

                max_match_count = max(len(m) for _, m, _ in candidates)

                top_matches = [(pid, m, mm) for pid, m, mm in candidates if len(m) == max_match_count]

                project_ids = [pid for pid, _, _ in top_matches]
                matched_fields = {pid: m for pid, m, _ in top_matches}
                mismatched_fields = {pid: mm for pid, _, mm in top_matches}

                logger.info(f"[MATCHING] ✅ Top matching project(s): {project_ids}")
                return project_ids, matched_fields, mismatched_fields

            except Exception as e:
                logger.error(f"[MATCHING] Error during prioritized matching: {e}", exc_info=True)
                return [], {}, {}

    def mark_document_for_manual_review(self, document_id: int) -> None:
        """
        Sets the 'needs_manual_review' flag to True for the specified document.
        """
        with self.db_client.session_scope() as session:
            try:
                document = session.query(Document).filter_by(id=document_id).one_or_none()

                if not document:
                    raise ValueError(f"Document with ID {document_id} not found.")

                document.needs_manual_review = True
                document.updated_at = datetime.utcnow()

                session.add(document)  # Optional, SQLAlchemy will track it, but explicit is fine
                session.commit()

                logger.info(f"Document ID {document_id} marked for manual review.")

            except Exception as e:
                logger.error(f" Failed to mark document {document_id} for manual review: {e}", exc_info=True)
                raise RuntimeError(f"Failed to update document {document_id}: {e}")



