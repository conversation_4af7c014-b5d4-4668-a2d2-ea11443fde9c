from sqlalchemy import Column, Integer, String, DateTime, func, Foreign<PERSON>ey, BigInteger, Text, Boolean, UniqueConstraint
from sqlalchemy.orm import declarative_base, relationship

Base = declarative_base()

class DataSourceType(Base):
    __tablename__ = "data_source_type"
    __table_args__ = {'schema': 'fileflow'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    type = Column(String(50), nullable=False)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)

    data_sources = relationship("DataSource", back_populates="type")


class DataSource(Base):
    __tablename__ = "data_source"
    __table_args__ = {'schema': 'fileflow'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(50), nullable=False)
    data_source_type_id = Column(Integer, ForeignKey("fileflow.data_source_type.id"), nullable=False)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)

    type = relationship("DataSourceType", back_populates="data_sources")
    company_sources = relationship("CompanyDataSource", back_populates="data_source")


class CompanyDataSource(Base):
    __tablename__ = "company_data_source"
    __table_args__ = {'schema': 'fileflow'}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    company_id = Column(BigInteger, ForeignKey("website.company.company_id"), nullable=False)
    data_source_id = Column(Integer, ForeignKey("fileflow.data_source.id"), nullable=False)
    connection_config = Column(Text, nullable=True)
    active = Column(Boolean, nullable=True, default=True)
    last_extraction = Column(DateTime, nullable=True)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)

    data_source = relationship("DataSource", back_populates="company_sources")
    documents = relationship("Document", back_populates="company_data_source")


class DocumentType(Base):
    __tablename__ = "document_type"
    __table_args__ = {'schema': 'fileflow'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    type = Column(String(50), nullable=False)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)


class Document(Base):
    __tablename__ = "document"
    __table_args__ = {'schema': 'fileflow'}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    company_data_source_id = Column(BigInteger, ForeignKey("fileflow.company_data_source.id"), nullable=False)
    original_name = Column(String(255), nullable=False)
    document_name = Column(String(255), nullable=True)
    storage_location = Column(String(500), nullable=False)
    document_type_id = Column(Integer, ForeignKey("fileflow.document_type.id"), nullable=True)
    document_metadata = Column("metadata", Text, nullable=True)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)
    needs_manual_review = Column(Boolean, nullable=False, default=False)

    company_data_source = relationship("CompanyDataSource", back_populates="documents")
    document_type = relationship("DocumentType")


class DocumentField(Base):
    __tablename__ = "document_fields"
    __table_args__ = {'schema': 'fileflow'}

    id = Column(Integer, primary_key=True)
    document_type_id = Column(Integer, ForeignKey("fileflow.document_type.id"), nullable=False)
    field_name = Column(String(100), nullable=False)
    is_required = Column(Boolean, nullable=False, default=False)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)


class DocumentFieldValue(Base):
    __tablename__ = "document_field_values"
    __table_args__ = {'schema': 'fileflow'}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    document_id = Column(BigInteger, ForeignKey("fileflow.document.id"), nullable=False)
    document_field_id = Column(Integer, ForeignKey("fileflow.document_fields.id"), nullable=False)
    field_value = Column(Text, nullable=True)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)


class Email(Base):
    __tablename__ = "email"
    __table_args__ = {'schema': 'fileflow'}

    id = Column(BigInteger, primary_key=True)
    data_source_id = Column(Integer, ForeignKey("fileflow.data_source.id"), nullable=False)
    email_provider_id = Column(String(255), nullable=False)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)


class Service(Base):
    __tablename__ = "service"
    __table_args__ = {'schema': 'fileflow'}

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(String(500), nullable=True)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)


class Status(Base):
    __tablename__ = "status"
    __table_args__ = {'schema': 'fileflow'}

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(String(500), nullable=True)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)


class DocumentServiceStatus(Base):
    __tablename__ = "document_service_status"
    __table_args__ = {'schema': 'fileflow'}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    document_id = Column(BigInteger, ForeignKey("fileflow.document.id"), nullable=False)
    service_id = Column(Integer, ForeignKey("fileflow.service.id"), nullable=False)
    status_id = Column(Integer, ForeignKey("fileflow.status.id"), nullable=False)
    service_metadata = Column("metadata",Text, nullable=True)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)

class NameConvention(Base):
    __tablename__ = "name_conventions"
    __table_args__ = (
        UniqueConstraint('company_data_destination_id', 'document_type_id', 'name_template', name='UQ_name_conventions_destination_type'),
        {'schema': 'fileflow'}
    )

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    company_data_destination_id = Column(BigInteger, ForeignKey("fileflow.company_data_destination.id"), nullable=False)
    document_type_id = Column(Integer, ForeignKey("fileflow.document_type.id"), nullable=False)
    name_template = Column(String(500), nullable=False)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True)

class FilevineField(Base):
    __tablename__ = 'filevine_fields'
    __table_args__ = {'schema': 'fileflow'}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    filevine_project_id = Column(BigInteger, nullable=False)
    field_name = Column(String(255), nullable=False)
    friendly_name = Column(String(255), nullable=True)
    field_type = Column(String(100), nullable=True)
    field_value = Column(String(500), nullable=True)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=True)

class FilevineProject(Base):
    __tablename__ = 'filevine_projects'
    __table_args__ = {'schema': 'fileflow'}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    company_data_destination_id = Column(BigInteger, nullable=False)
    project_id = Column(String(100), nullable=False)
    org_id = Column(String(100), nullable=False)
    client_id = Column(String(100), nullable=True)
    phase_name = Column(String(255), nullable=True)
    client_name = Column(String(255), nullable=True)
    project_email_address = Column(String(255), nullable=True)
    project_or_client_name = Column(String(255), nullable=True)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=True)
